import { Action, ActionPanel, Form, showToast, Toast, useNavigation } from "@raycast/api";
import { useState } from "react";
import axios, { AxiosError } from "axios";

// Constants for ONES API
const BASE_URL = "https://nones.xylink.com";
const TEAM_ID = "AQzvsooq";
const AUTH_TOKEN = "9RKKluMTWdKx6shep4wWGZfloeTBV0TZJIBK19exadyEmYyjWIZXKByRSPz0gpgD";
const USER_ID = "KXymEf3B";

//产研部项目-2025
const PROJECT_ID = "7ww6Mj4C45Rw6tzL";
// 需求
const ISSUE_TYPE_ID = "Taqqzj1K";

// Options for dropdowns
const priorityOptions = [
  { value: "ForpWegK", title: "P0" },
  { value: "NJGJGTTQ", title: "P1" },
];

// DtKPaMvc
const firstLevelProjectOptions = [
  { value: "PaqpZtgo", title: "分区云五期" },
  { value: "M63uKdRY", title: "公有云3.12" },
];
// Dnu9jCv5
const secondLevelProjectOptions = [
  { value: "UawzCALu", title: "分区云五期-5.2" },
  { value: "5tixW93x", title: "公有云3.12-软终端" },
];
// U8n1AAHc
const salesProjectOptions = [
  { value: "7ww6Mj4C45Rw6tzL", title: "产研部项目-2025" },
  { value: "HZuW8CJjAseystKy", title: "陕西省政务大数据服务中心" },
  { value: "HZuW8CJjRD8Tikp3", title: "交行高可用2025" },
];

// 5sf3ReEe
const customerOptions = [
  { value: "YKtQsH4P", title: "无" },
  { value: "C5TXcmCw", title: "陕西大数据" },
  { value: "Sx3rBuVG", title: "交行" },
  { value: "TvkjSffB", title: "邮储" },
];
// 3SweHRyg
const generalRequirementOptions = [
  { value: "TvYHKthS", title: "Y" },
  { value: "8TCxBzZj", title: "N" },
];
// GzZgmPkJ
const commitOptions = [
  { value: "CPG7CEAY", title: "Tbd" },
  { value: "66MCT16y", title: "Y" },
];
// field029
const productOptions = [
  { value: "X6USVB2TRgqsSyxw", title: "云平台" },
  { value: "7ww6Mj4CDiQUef3d", title: "软终端" },
];
// field030
const functionModuleOptions = [
  { value: "SovnmPMj", title: "组网部署" },
  { value: "RfNUsdfA", title: "manager" },
  { value: "RcS8HBDr", title: "webrtc/小程序" },
];
// 5nhRZCoZ
const iterationEnvironmentOptions = [
  { value: "BjjkJYxh", title: "5.2所有环境" },
  { value: "PYqUz3Y5", title: "arm+麒麟v10sp1+人大金仓" },
  { value: "Xe7ocDKk", title: "arm+麒麟v10sp2+pgsql" },
  { value: "NFF3LXZh", title: "x86+麒麟v10sp1+oceanbase" },
  { value: "FX79MvYo", title: "x86+suse+mysql" },
  { value: "8Sbi7BFT", title: "公有云" },
];
// field011
const iterationOptions = [
  { value: "3M2HHZKr", title: "5.2-250926" },
  { value: "VmHzUGMZ", title: "hotfix-2025" },
];
// 9dqmWrw3
const foOptions = [
  { value: "", title: "未分配" },
  { value: "LzAVdBM9", title: "杨佳佳" },
  { value: "RmU1rL6u", title: "刘建" },
  { value: "SPzxxojG", title: "董成希" },
  { value: "XWEioSYq", title: "罗晶辉" },
  { value: "L1HdaxNo", title: "刘洋" },
];
// LEfPZweg
const qaOwnerOptions = [
  { value: "", title: "未分配" },
  { value: "2e8WZzyd", title: "刘阳" },
  { value: "XTecG6dM", title: "聂海情" },
];

// API Response interfaces
interface TaskModel {
  uuid: string;
  assign: string;
  project_uuid: string;
  issue_type_uuid: string;
  summary: string;
  desc_rich: string;
  priority: string;
  field_values: Array<{
    field_type: number;
    field_uuid: string;
    value: string | string[] | number;
  }>;
}

interface BadTask {
  uuid: string;
  code: number;
  desc: string;
}

interface CreateTaskResponse {
  tasks: TaskModel[];
  bad_tasks: BadTask[];
}

// Error code mappings
const ERROR_CODES = {
  200: "操作成功",
  403: "没有访问该工作项的权限",
  404: "工作项不存在",
  500: "接口错误",
  603: "找不到需要修改的task",
  801: "参数无效"
} as const;

interface TaskFormValues {
  taskSummary: string;
  taskDescription: string;
  projectId: string;
  issueTypeUuid: string;
  priorityId: string;
  firstLevelProjectValue: string;
  secondLevelProjectValue: string;
  salesProjectValue: string[];
  customerAbbreviationValue: string;
  generalRequirementValue: string;
  commitValue: string;
  productValue: string[];
  functionModuleValue: string[];
  iterationEnvironmentValue: string[];
  iterationValue: string;
  foValue?: string[];
  qaOwnerValue?: string;
  testEventTime?: Date;
  hotfixTime?: Date;
}

export default function Command() {
  const { pop } = useNavigation();
  const [isLoading, setIsLoading] = useState(false);

  // Helper function to get error message by code
  function getErrorMessage(code: number): string {
    return ERROR_CODES[code as keyof typeof ERROR_CODES] || `未知错误 (${code})`;
  }

  // Form validation
  function validateForm(values: TaskFormValues): string | null {
    if (!values.taskSummary?.trim()) {
      return "任务标题不能为空";
    }
    if (values.taskSummary.trim().length < 2) {
      return "任务标题至少需要2个字符";
    }
    return null;
  }

  async function handleSubmit(values: TaskFormValues) {
    setIsLoading(true);

    try {
      // Validate form before submission
      const validationError = validateForm(values);
      if (validationError) {
        await showToast({
          style: Toast.Style.Failure,
          title: "表单验证失败",
          message: validationError
        });
        return;
      }

      const taskUuid = USER_ID + generateRandomString(8);

      const payload = {
        tasks: [
          {
            uuid: taskUuid,
            assign: USER_ID,
            project_uuid: PROJECT_ID,
            issue_type_uuid: ISSUE_TYPE_ID,
            summary: values.taskSummary.trim(),
            desc_rich: values.taskDescription?.trim() || "",
            priority: values.priorityId,
            field_values: [
              // Single value fields (field_type 1, 7, 8)
              {
                field_type: 1,
                field_uuid: "DtKPaMvc",
                value: values.firstLevelProjectValue
              },
              {
                field_type: 1,
                field_uuid: "Dnu9jCv5",
                value: values.secondLevelProjectValue
              },
              // Array value fields (field_type 50, 44, 46, 16, 13)
              {
                field_type: 50,
                field_uuid: "U8n1AAHc",
                value: values.salesProjectValue
              },
              {
                field_type: 1,
                field_uuid: "5sf3ReEe",
                value: values.customerAbbreviationValue
              },
              {
                field_type: 1,
                field_uuid: "3SweHRyg",
                value: values.generalRequirementValue
              },
              {
                field_type: 1,
                field_uuid: "GzZgmPkJ",
                value: values.commitValue
              },
              {
                field_type: 44,
                field_uuid: "field029",
                value: values.productValue
              },
              {
                field_type: 46,
                field_uuid: "field030",
                value: values.functionModuleValue
              },
              {
                field_type: 16,
                field_uuid: "5nhRZCoZ",
                value: values.iterationEnvironmentValue
              },
              {
                field_type: 7,
                field_uuid: "field011",
                value: values.iterationValue
              },
              // Only include FO if a value is selected (not empty array and not contains empty string)
              ...(values.foValue && values.foValue.length > 0 && !values.foValue.includes("") ? [{
                field_type: 13,
                field_uuid: "9dqmWrw3",
                value: values.foValue
              }] : []),
              // Only include QA Owner if a value is selected (not empty string)
              ...(values.qaOwnerValue && values.qaOwnerValue.trim() !== "" ? [{
                field_type: 8,
                field_uuid: "LEfPZweg",
                value: values.qaOwnerValue
              }] : []),
              // Only include test event time if a value is selected
              ...(values.testEventTime ? [{
                field_type: 5,
                field_uuid: "QMNg83DF",
                value: Math.floor(values.testEventTime.getTime() / 1000)
              }] : []),
              // Only include hotfix time if a value is selected
              ...(values.hotfixTime ? [{
                field_type: 5,
                field_uuid: "X9RTbfkf",
                value: Math.floor(values.hotfixTime.getTime() / 1000)
              }] : [])
            ]
          }
        ]
      };

      const response = await axios.post<CreateTaskResponse>(
        `${BASE_URL}/project/api/project/team/${TEAM_ID}/tasks/add2`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "Ones-Auth-Token": AUTH_TOKEN,
            "Ones-User-Id": USER_ID,
            "Referer": BASE_URL,
            "cache-control": "no-cache"
          }
        }
      );

      // Handle successful response (200)
      if (response.status === 200) {
        const { tasks, bad_tasks } = response.data;

        // Check if there are any failed tasks
        if (bad_tasks && bad_tasks.length > 0) {
          const failedTask = bad_tasks[0];
          const errorMessage = getErrorMessage(failedTask.code);
          await showToast({
            style: Toast.Style.Failure,
            title: "任务创建失败",
            message: `${errorMessage}: ${failedTask.desc}`
          });
          return;
        }

        // Check if task was successfully created
        if (tasks && tasks.length > 0) {
          const createdTask = tasks[0];
          await showToast({
            style: Toast.Style.Success,
            title: "任务创建成功",
            message: `成功创建任务: ${createdTask.summary}`
          });
          pop();
        } else {
          await showToast({
            style: Toast.Style.Failure,
            title: "任务创建失败",
            message: "未返回创建的任务信息"
          });
        }
      } else {
        // Handle other status codes
        const errorMessage = getErrorMessage(response.status);
        throw new Error(`${errorMessage} (${response.status})`);
      }
    } catch (error) {
      console.error("Error creating task:", error);

      // Handle axios errors with specific status codes
      if (error instanceof AxiosError && error.response) {
        const statusCode = error.response.status;
        const errorMessage = getErrorMessage(statusCode);

        await showToast({
          style: Toast.Style.Failure,
          title: "任务创建失败",
          message: `${errorMessage} (${statusCode})`
        });
      } else {
        // Handle other types of errors (network, etc.)
        await showToast({
          style: Toast.Style.Failure,
          title: "任务创建失败",
          message: error instanceof Error ? error.message : "网络错误或未知错误"
        });
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form
      isLoading={isLoading}
      actions={
        <ActionPanel>
          <Action.SubmitForm title="Create Task" onSubmit={handleSubmit} />
        </ActionPanel>
      }
    >
      <Form.TextField
        id="taskSummary"
        title="标题"
        placeholder="需求标题"
        info="必填项：任务标题不能为空"
      />

      <Form.TextArea
        id="taskDescription"
        title="描述"
        placeholder="需求描述"
      />

      <Form.Dropdown id="priorityId" title="优先级" defaultValue="ForpWegK">
        {priorityOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="firstLevelProjectValue" title="一级项目" defaultValue="PaqpZtgo">
        {firstLevelProjectOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="secondLevelProjectValue" title="二级项目" defaultValue="UawzCALu">
        {secondLevelProjectOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="salesProjectValue" title="销售项目" defaultValue={["7ww6Mj4C45Rw6tzL"]}>
        {salesProjectOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="customerAbbreviationValue" title="客户简称" defaultValue="YKtQsH4P">
        {customerOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="generalRequirementValue" title="通用需求" defaultValue="TvYHKthS">
        {generalRequirementOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="commitValue" title="Commit" defaultValue="CPG7CEAY">
        {commitOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="productValue" title="所属产品" defaultValue={["X6USVB2TRgqsSyxw"]}>
        {productOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.TagPicker id="functionModuleValue" title="所属功能模块" defaultValue={["SovnmPMj"]}>
        {functionModuleOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.TagPicker id="iterationEnvironmentValue" title="迭代环境" defaultValue={["BjjkJYxh"]}>
        {iterationEnvironmentOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="iterationValue" title="所属迭代" defaultValue="3M2HHZKr">
        {iterationOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="foValue" title="FO" defaultValue={[]}>
        {foOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="qaOwnerValue" title="QA Owner" defaultValue="">
        {qaOwnerOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.DatePicker
        id="testEventTime"
        title="提测时间"
        type={Form.DatePicker.Type.Date}
      />

      <Form.DatePicker
        id="hotfixTime"
        title="Hotfix时间"
        type={Form.DatePicker.Type.Date}
      />
    </Form>
  );
}

// Helper function to generate a random string for task UUID
function generateRandomString(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}
