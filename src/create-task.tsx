import { Action, ActionPanel, Form, showToast, Toast, useNavigation } from "@raycast/api";
import { useState } from "react";
import axios from "axios";

// Constants for ONES API
const BASE_URL = "https://nones.xylink.com";
const TEAM_ID = "AQzvsooq";
const AUTH_TOKEN = "9RKKluMTWdKx6shep4wWGZfloeTBV0TZJIBK19exadyEmYyjWIZXKByRSPz0gpgD";
const USER_ID = "KXymEf3B";

//产研部项目-2025
const PROJECT_ID = "7ww6Mj4C45Rw6tzL";
// 需求
const ISSUE_TYPE_ID = "Taqqzj1K";

// Options for dropdowns
const priorityOptions = [
  { value: "ForpWegK", title: "P0" },
  { value: "Low", title: "待定" },
];

const firstLevelProjectOptions = [
  { value: "PaqpZtgo", title: "分区云五期" },
  { value: "Option2", title: "一级项目2" },
];

const secondLevelProjectOptions = [
  { value: "UawzCALu", title: "分区云五期-5.2" },
  { value: "Option2", title: "二级项目2" },
];

const salesProjectOptions = [
  { value: "7ww6Mj4C45Rw6tzL", title: "产研部项目-2025" },
  { value: "Option2", title: "销售项目2" },
];

const customerOptions = [
  { value: "YKtQsH4P", title: "无" },
  { value: "Option2", title: "客户2" },
];

const generalRequirementOptions = [
  { value: "TvYHKthS", title: "Y" },
  { value: "Option2", title: "需求2" },
];

const commitOptions = [
  { value: "CPG7CEAY", title: "Tbd" },
  { value: "Option2", title: "Commit2" },
];

const productOptions = [
  { value: "X6USVB2TRgqsSyxw", title: "云平台" },
  { value: "Option2", title: "产品2" },
];

const functionModuleOptions = [
  { value: "SovnmPMj", title: "组网部署" },
  { value: "Option2", title: "功能模块2" },
  { value: "Option3", title: "功能模块3" },
];

const iterationEnvironmentOptions = [
  { value: "BjjkJYxh", title: "5.2所有环境" },
  { value: "Option2", title: "环境2" },
];

const iterationOptions = [
  { value: "3M2HHZKr", title: "5.2-250926" },
  { value: "Option2", title: "迭代2" },
];

const foOptions = [
  { value: "LzAVdBM9", title: "杨佳佳" },
  { value: "Option2", title: "FO2" },
];

const qaOwnerOptions = [
  { value: "2e8WZzyd", title: "刘阳" },
  { value: "Option2", title: "QA2" },
];

interface TaskFormValues {
  taskSummary: string;
  taskDescription: string;
  projectId: string;
  issueTypeUuid: string;
  priorityId: string;
  firstLevelProjectValue: string;
  secondLevelProjectValue: string;
  salesProjectValue: string[];
  customerAbbreviationValue: string;
  generalRequirementValue: string;
  commitValue: string;
  productValue: string[];
  functionModuleValue: string[];
  iterationEnvironmentValue: string[];
  iterationValue: string;
  foValue: string[];
  qaOwnerValue: string;
}

export default function Command() {
  const { pop } = useNavigation();
  const [isLoading, setIsLoading] = useState(false);

  async function handleSubmit(values: TaskFormValues) {
    setIsLoading(true);
    
    try {
      const taskUuid = USER_ID + generateRandomString(8);
      
      const payload = {
        tasks: [
          {
            uuid: taskUuid,
            assign: USER_ID,
            project_uuid: PROJECT_ID,
            issue_type_uuid: ISSUE_TYPE_ID,
            summary: values.taskSummary,
            desc_rich: values.taskDescription,
            priority: values.priorityId,
            field_values: [
              {
                field_type: 1,
                field_uuid: "DtKPaMvc",
                value: values.firstLevelProjectValue
              },
              {
                field_type: 1,
                field_uuid: "Dnu9jCv5",
                value: values.secondLevelProjectValue
              },
              {
                field_type: 50,
                field_uuid: "U8n1AAHc",
                value: values.salesProjectValue.join(",")
              },
              {
                field_type: 1,
                field_uuid: "5sf3ReEe",
                value: values.customerAbbreviationValue
              },
              {
                field_type: 1,
                field_uuid: "3SweHRyg",
                value: values.generalRequirementValue
              },
              {
                field_type: 1,
                field_uuid: "GzZgmPkJ",
                value: values.commitValue
              },
              {
                field_type: 44,
                field_uuid: "field029",
                value: values.productValue.join(",")
              },
              {
                field_type: 46,
                field_uuid: "field030",
                value: values.functionModuleValue.join(",")
              },
              {
                field_type: 16,
                field_uuid: "5nhRZCoZ",
                value: values.iterationEnvironmentValue.join(",")
              },
              {
                field_type: 7,
                field_uuid: "field011",
                value: values.iterationValue
              },
              {
                field_type: 13,
                field_uuid: "9dqmWrw3",
                value: values.foValue.join(",")
              },
              {
                field_type: 8,
                field_uuid: "LEfPZweg",
                value: values.qaOwnerValue
              }
            ]
          }
        ]
      };

      const response = await axios.post(
        `${BASE_URL}/project/api/project/team/${TEAM_ID}/tasks/add2`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "Ones-Auth-Token": AUTH_TOKEN,
            "Ones-User-Id": USER_ID,
            "Referer": BASE_URL,
            "cache-control": "no-cache"
          }
        }
      );

      if (response.status === 200) {
        await showToast({
          style: Toast.Style.Success,
          title: "Task Created",
          message: `Successfully created task: ${values.taskSummary} ${taskUuid}`
        });
        pop();
      } else {
        throw new Error(`Error: ${response.status}`);
      }
    } catch (error) {
      console.error("Error creating task:", error);
      await showToast({
        style: Toast.Style.Failure,
        title: "Failed to Create Task",
        message: error instanceof Error ? error.message : "Unknown error"
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form
      isLoading={isLoading}
      actions={
        <ActionPanel>
          <Action.SubmitForm title="Create Task" onSubmit={handleSubmit} />
        </ActionPanel>
      }
    >
      <Form.TextField
        id="taskSummary"
        title="标题"
        placeholder="需求标题"
        required
      />
      
      <Form.TextArea
        id="taskDescription"
        title="描述"
        placeholder="需求描述"
      />

      <Form.Dropdown id="priorityId" title="优先级" defaultValue="ForpWegK">
        {priorityOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="firstLevelProjectValue" title="一级项目" defaultValue="PaqpZtgo">
        {firstLevelProjectOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="secondLevelProjectValue" title="二级项目" defaultValue="UawzCALu">
        {secondLevelProjectOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="salesProjectValue" title="销售项目" defaultValue={["7ww6Mj4C45Rw6tzL"]}>
        {salesProjectOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="customerAbbreviationValue" title="客户简称" defaultValue="YKtQsH4P">
        {customerOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="generalRequirementValue" title="通用需求" defaultValue="TvYHKthS">
        {generalRequirementOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.Dropdown id="commitValue" title="Commit" defaultValue="CPG7CEAY">
        {commitOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="productValue" title="所属产品" defaultValue={["X6USVB2TRgqsSyxw"]}>
        {productOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.TagPicker id="functionModuleValue" title="所属功能模块" defaultValue={["SovnmPMj"]}>
        {functionModuleOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.TagPicker id="iterationEnvironmentValue" title="迭代环境" defaultValue={["BjjkJYxh"]}>
        {iterationEnvironmentOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="iterationValue" title="所属迭代" defaultValue="3M2HHZKr">
        {iterationOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>

      <Form.TagPicker id="foValue" title="FO" defaultValue={["LzAVdBM9"]}>
        {foOptions.map((option) => (
          <Form.TagPicker.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.TagPicker>

      <Form.Dropdown id="qaOwnerValue" title="QA Owner" defaultValue="2e8WZzyd">
        {qaOwnerOptions.map((option) => (
          <Form.Dropdown.Item key={option.value} value={option.value} title={option.title} />
        ))}
      </Form.Dropdown>
    </Form>
  );
}

// Helper function to generate a random string for task UUID
function generateRandomString(length: number): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}
