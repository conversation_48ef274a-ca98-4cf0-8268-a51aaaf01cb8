# ONES Task Creator Raycast Extension

This Raycast extension allows you to quickly create tasks in the ONES project management system directly from Raycast.

## Features

- Create tasks with a simple form interface
- Set task summary (required user input)
- Select from predefined options for various task fields
- Multi-select functionality for "所属功能模块" (Function Module)
- Single-select for all other fields
- Automatically sends the request to the ONES API

## Installation

1. Clone this repository
2. Navigate to the directory
3. Install dependencies:
   ```
   npm install
   ```
4. Link the extension to Raycast:
   ```
   npm run dev
   ```

## Usage

1. Open Raycast
2. Search for "Create ONES Task"
3. Fill in the required fields:
   - Task Summary (required user input)
   - Description (optional)
   - Select options for all other fields
   - For "所属功能模块" (Function Module), you can select multiple options
4. Click "Create Task" to submit

## Configuration

You can modify the available options and default values in the `src/create-task.tsx` file:

- Update the `BASE_URL`, `TEAM_ID`, `AUTH_TOKEN`, and `USER_ID` constants to match your ONES instance
- Modify the options arrays to include your specific project options

## Development

To make changes to the extension:

1. Make your changes to the source code
2. Run `npm run dev` to test the extension in Raycast
3. Use `npm run build` to build the extension for distribution

## Troubleshooting

If you encounter issues:

1. Check the console logs in Raycast (⌘+K, then type "Debug")
2. Verify your ONES API credentials and permissions
3. Ensure your network can reach the ONES server

## License

MIT
